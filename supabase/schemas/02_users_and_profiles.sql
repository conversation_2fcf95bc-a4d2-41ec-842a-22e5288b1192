-- User Profiles and Authentication
-- This file contains user profile management and authentication-related functions

-- Profile table
CREATE TABLE IF NOT EXISTS "public"."profile" (
	"user_id" "uuid" NOT NULL,
	"email" "text" NOT NULL,
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."profile" OWNER TO "postgres";
COMMENT ON TABLE "public"."profile" IS 'User profile containing personal information and preferences';

-- Primary key constraint
ALTER TABLE ONLY "public"."profile"
ADD CONSTRAINT "profile_pkey" PRIMARY KEY ("user_id");

-- Enable Row Level Security
ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;

-- Utility function for updating timestamps
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column" () RETURNS "trigger" LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$ 
BEGIN 
	NEW.updated_at = timezone('utc', now());
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."update_updated_at_column" () OWNER TO "postgres";
COMMENT ON FUNCTION "public"."update_updated_at_column" () IS 'Updates the updated_at timestamp when a record is modified';

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION "public"."handle_new_user" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN
	INSERT INTO public.profile (user_id, email, full_name)
	VALUES (
		NEW.id,
		NEW.email,
		NEW.raw_user_meta_data->>'full_name'
	);
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."handle_new_user" () OWNER TO "postgres";

-- Function to apply pending invites when user signs up
CREATE OR REPLACE FUNCTION "public"."apply_pending_invites" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	v_invite public.invite %rowtype;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN 
	-- Loop through all pending invites for this email
	BEGIN 
		FOR v_invite IN (
			SELECT *
			FROM public.invite
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at > now()
		) LOOP 
			BEGIN 
				-- Store resource_type as text to avoid casting issues
				v_resource_type := v_invite.resource_type::text;
				
				IF v_resource_type = 'organization' THEN 
					v_entity_type := 'organization'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					IF v_invite.role = 'member' THEN 
						v_role := 'viewer'::public.membership_role;
					ELSE 
						v_role := v_invite.role::public.membership_role;
					END IF;
				ELSIF v_resource_type = 'client' THEN 
					v_entity_type := 'client'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSIF v_resource_type = 'project' THEN 
					v_entity_type := 'project'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSE 
					CONTINUE;
				END IF;
				
				BEGIN
					INSERT INTO public.membership(user_id, role, entity_type, entity_id)
					VALUES (NEW.user_id, v_role, v_entity_type, v_entity_id) 
					ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;
					
					UPDATE public.invite
					SET status = 'accepted'::public.invite_status,
						updated_at = now(),
						updated_by = NEW.user_id
					WHERE invite_id = v_invite.invite_id;
				EXCEPTION
					WHEN OTHERS THEN 
						-- Log error but continue processing other invites
						RAISE NOTICE 'Error processing invite %: %', v_invite.invite_id, SQLERRM;
				END;
			EXCEPTION
				WHEN OTHERS THEN 
					-- Log error but continue processing other invites
					RAISE NOTICE 'Error mapping invite %: %', v_invite.invite_id, SQLERRM;
			END;
		END LOOP;
		
		BEGIN
			UPDATE public.invite
			SET status = 'expired'::public.invite_status,
				updated_at = now(),
				updated_by = NEW.user_id
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at <= now();
		EXCEPTION
			WHEN OTHERS THEN 
				-- Log error but allow user creation to continue
				RAISE NOTICE 'Error expiring outdated invites: %', SQLERRM;
		END;
	EXCEPTION
		WHEN OTHERS THEN 
			-- Log error but allow user creation to continue
			RAISE NOTICE 'Error in apply_pending_invites: %', SQLERRM;
	END;
	
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."apply_pending_invites" () OWNER TO "postgres";

-- Triggers
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "trg_profile_invites"
AFTER INSERT ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."apply_pending_invites" ();

-- RLS Policies
CREATE POLICY "Users can view their own profile" ON "public"."profile" FOR
SELECT
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can update their own profile" ON "public"."profile"
FOR UPDATE
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can insert their own profile" ON "public"."profile" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);
