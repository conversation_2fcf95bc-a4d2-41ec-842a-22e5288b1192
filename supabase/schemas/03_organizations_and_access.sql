-- Organizations, Clients, Membership, and Invites
-- This file contains the core organizational structure and access control system

-- Organization table
CREATE TABLE IF NOT EXISTS "public"."organization" (
	"org_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."organization" OWNER TO "postgres";
COMMENT ON TABLE "public"."organization" IS 'Organization that group users and clients together';

-- Client table
CREATE TABLE IF NOT EXISTS "public"."client" (
	"client_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"internal_url" "text",
	"internal_url_description" "text",
	"client_url" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"logo_url" "text",
	"org_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."client" OWNER TO "postgres";
COMMENT ON TABLE "public"."client" IS 'Client contains all the clients of an organization';

-- Membership table (polymorphic access control)
CREATE TABLE IF NOT EXISTS "public"."membership" (
	"membership_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"user_id" "uuid" NOT NULL,
	"role" "public"."membership_role" NOT NULL,
	"entity_type" "public"."entity_type" NOT NULL,
	"entity_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."membership" OWNER TO "postgres";
COMMENT ON TABLE "public"."membership" IS 'Polymorphic membership table for organization, client, and project access control';

-- Invite table
CREATE TABLE IF NOT EXISTS "public"."invite" (
	"invite_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"resource_type" "public"."invite_resource_type" NOT NULL,
	"resource_id" "uuid" NOT NULL,
	"role" "text" NOT NULL,
	"invitee_email" "text" NOT NULL,
	"token_hash" character(64) NOT NULL,
	"status" "public"."invite_status" DEFAULT 'pending'::"public"."invite_status" NOT NULL,
	"inviter_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_by" "uuid",
	"expires_at" timestamp with time zone NOT NULL
);

ALTER TABLE "public"."invite" OWNER TO "postgres";

-- Primary key constraints
ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_pkey" PRIMARY KEY ("org_id");

ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_name_key" UNIQUE ("name");

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_pkey" PRIMARY KEY ("client_id");

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_name_org_id_key" UNIQUE ("name", "org_id");

ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_pkey" PRIMARY KEY ("membership_id");

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_pkey" PRIMARY KEY ("invite_id");

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_invitee_email_resource_type_resource_id_key" UNIQUE ("invitee_email", "resource_type", "resource_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."client"
ADD CONSTRAINT "client_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "public"."organization" ("org_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_inviter_id_fkey" FOREIGN KEY ("inviter_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invite"
ADD CONSTRAINT "invite_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."membership"
ADD CONSTRAINT "membership_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."organization"
ADD CONSTRAINT "organization_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "invite_resource_idx" ON "public"."invite" USING "btree" ("resource_type", "resource_id");

CREATE INDEX "invite_token_hash_idx" ON "public"."invite" USING "btree" ("token_hash")
WHERE
	("status" = 'pending'::"public"."invite_status");

CREATE INDEX "idx_invite_email_status_expires" ON "public"."invite" USING "btree" ("invitee_email", "status", "expires_at");

CREATE INDEX "membership_entity_idx" ON "public"."membership" USING "btree" ("entity_type", "entity_id");

CREATE UNIQUE INDEX "membership_unique_entity_user" ON "public"."membership" USING "btree" ("entity_type", "entity_id", "user_id");

CREATE INDEX "membership_user_idx" ON "public"."membership" USING "btree" ("user_id");

-- Enable Row Level Security
ALTER TABLE "public"."organization" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."client" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."membership" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."invite" ENABLE ROW LEVEL SECURITY;

-- Access Control Functions
CREATE OR REPLACE FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS TABLE (
	"entity_type" "public"."entity_type",
	"entity_id" "uuid"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	-- Return the entity itself
	RETURN QUERY SELECT entity_type_param, entity_id_param;

	-- Return ancestors based on entity type
	IF entity_type_param = 'project' THEN
		-- Project -> Client -> Organization
		RETURN QUERY
		SELECT 'client'::public.entity_type, p.client_id
		FROM public.project p
		WHERE p.project_id = entity_id_param;

		RETURN QUERY
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = entity_id_param;

	ELSIF entity_type_param = 'client' THEN
		-- Client -> Organization
		RETURN QUERY
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.client c
		WHERE c.client_id = entity_id_param;
	END IF;
END;
$$;

ALTER FUNCTION "public"."get_entity_ancestors" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

CREATE OR REPLACE FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN EXISTS (
		SELECT 1
		FROM public.membership m
		JOIN LATERAL public.get_entity_ancestors(entity_type_param, entity_id_param) a ON TRUE
		WHERE m.user_id = user_id_param
			AND m.entity_type = a.entity_type
			AND m.entity_id = a.entity_id
	);
END;
$$;

ALTER FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."has_entity_access" (
	"user_id_param" "uuid",
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if a user has access to an entity through direct membership or ancestor entities';

CREATE OR REPLACE FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
END;
$$;

ALTER FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."current_user_has_entity_access" (
	"entity_type_param" "public"."entity_type",
	"entity_id_param" "uuid"
) IS 'Checks if the current user has access to an entity';

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."organization" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."client" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."membership" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."invite" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();
